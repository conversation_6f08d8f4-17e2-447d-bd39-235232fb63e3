package com.ttb.top.web.customer360service.mapper

import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerProfileCustomerResponse
import com.ttb.top.web.customer360service.service.customerphonenumbers.implement.CustomerPhoneNumbersCacheServiceImpl
import com.ttb.top.web.customer360service.utils.ResourceReader
import spock.lang.Specification
import spock.lang.Subject

class CustomerProfilePhoneNumberMapperTest extends Specification {
    def resourceReader = new ResourceReader()
    def customerPhoneNumbersCacheService = Mock(CustomerPhoneNumbersCacheServiceImpl)

    @Subject
    CustomerProfilePhoneNumberMapper mapper = CustomerProfilePhoneNumberMapper.INSTANCE

    def "Should map feign customer phone number response to customer phone number response"() {
        given: "A mock customer phone numbers feign response"
        def feignCustomerPhoneResponse = resourceReader.readValue(
                "feign/customerdataservice/customerphonenumbers/get_customer_success.json"
                , FeignCustomerProfileCustomerResponse.class
        )
        def staffId = "12345"
        when: "Called"
        def actual = mapper.mapCustomerProfilePhoneResponseToCustomerPhoneResponseNumbers(feignCustomerPhoneResponse, customerPhoneNumbersCacheService, staffId)
        then: "Should return CustomerPhoneNumbersResponse"
        actual != null
        actual instanceof CustomerPhoneNumbersResponse
        noExceptionThrown()
    }
}
