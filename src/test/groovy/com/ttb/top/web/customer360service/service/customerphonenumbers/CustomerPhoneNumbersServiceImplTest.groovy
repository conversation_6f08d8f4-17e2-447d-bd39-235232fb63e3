package com.ttb.top.web.customer360service.service.customerphonenumbers

import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.exceptionmodel.exception.BadRequestException
import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.ttb.top.web.customer360service.feign.CustomerDataServiceFeignClient
import com.ttb.top.web.customer360service.model.customerphonenumbers.request.CustomerPhoneNumbersRequest
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomerProfileCustomerRequest
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerProfileCustomerResponse
import com.ttb.top.web.customer360service.service.customerphonenumbers.implement.CustomerPhoneNumbersServiceImpl
import com.ttb.top.web.customer360service.utils.MockResponseTest
import com.ttb.top.web.customer360service.utils.ResourceReader
import feign.FeignException
import feign.Request
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import spock.lang.Specification
import spock.lang.Subject

import static com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant.STAFF_ID


class CustomerPhoneNumbersServiceImplTest extends Specification {
    def httpHeaders = Mock(HttpHeaders)
    def customerDataServiceFeignClient = Mock(CustomerDataServiceFeignClient)
    def customerPhoneNumbersCacheService = Mock(CustomerPhoneNumbersCacheService)
    def resourceReader = new ResourceReader()

    @Subject
    def customerPhoneNumbersService = new CustomerPhoneNumbersServiceImpl(
            httpHeaders,
            customerDataServiceFeignClient,
            customerPhoneNumbersCacheService
    )

    def "when valid input should return success"() {
        given: "Valid headers and request"
        def request = CustomerPhoneNumbersRequest.builder().rmId("12345").build()
        httpHeaders.getFirst(STAFF_ID) >> "12345"
        and: "Mock customerDataServiceFeignClient response"
        def mockCustomerDataServiceFeignClient = mockGetCustomerSuccessResponse()
        customerDataServiceFeignClient.getPhoneNumbers(httpHeaders, _ as FeignCustomerProfileCustomerRequest) >> ResponseModel.success(mockCustomerDataServiceFeignClient)
        when: "Call customerPhoneNumbersService"
        def actual = customerPhoneNumbersService.getCustomerPhoneNumbers(request)
        then: "Response is not null"
        actual != null
        noExceptionThrown()
    }

    def "when request header is invalid should throw BadRequestException"() {
        given:
        httpHeaders.getFirst(STAFF_ID) >> ""
        def request = CustomerPhoneNumbersRequest.builder()
                .rmId("12345")
                .ecId(null)
                .build()

        when: "Call service"
        customerPhoneNumbersService.getCustomerPhoneNumbers(request)

        then: "A BadRequestException is thrown"
        thrown(BadRequestException)
    }

    def "when request is invalid should throw BadRequestException"() {
        given:
        httpHeaders.getFirst(STAFF_ID) >> ""
        def request = CustomerPhoneNumbersRequest.builder()
                .rmId("")
                .ecId(null)
                .build()

        when: "Call service"
        customerPhoneNumbersService.getCustomerPhoneNumbers(request)

        then: "A BadRequestException is thrown"
        thrown(BadRequestException)
    }

    def "when data is null should throw Data Not Found Exception"() {
        given: "Valid headers and request"
        def request = CustomerPhoneNumbersRequest.builder().rmId("12345").build()
        httpHeaders.getFirst(STAFF_ID) >> "12345"
        and: "Mock customerDataServiceFeignClient response"
        customerDataServiceFeignClient.getPhoneNumbers(httpHeaders, _ as FeignCustomerProfileCustomerRequest) >> ResponseModel.success(null)
        when: "Call customerPhoneNumbersService"
        customerPhoneNumbersService.getCustomerPhoneNumbers(request)
        then: "A GenericException Exception"
        thrown(GenericException)
    }

    def "when feign timeout exception show throw generic internal server exception"() {
        given: "Valid headers and request"
        def request = CustomerPhoneNumbersRequest.builder().rmId("12345").build()
        httpHeaders.getFirst(STAFF_ID) >> "12345"
        and: "Mock customerDataServiceFeignClient and get response FeignException 9002"
        def mapper = new ObjectMapper()
        customerDataServiceFeignClient.getPhoneNumbers(httpHeaders, _ as FeignCustomerProfileCustomerRequest) >> {
            throw MockResponseTest.createFeignException(
                    HttpStatus.REQUEST_TIMEOUT.value(),
                    mapper.writeValueAsString([
                            status: [
                                    code       : "9002",
                                    header     : "",
                                    description: "Request Timeout"
                            ],
                            data  : null
                    ]),
                    Request.HttpMethod.POST)
        }
        when: "Call customerPhoneNumbersService"
        customerPhoneNumbersService.getCustomerPhoneNumbers(request)
        then: "A FeignException is thrown"
        thrown(FeignException)
    }

    def mockGetCustomerSuccessResponse() {
        return resourceReader.readValue(
                "feign/customerdataservice/customerphonenumbers/get_customer_success.json",
                FeignCustomerProfileCustomerResponse.class
        )
    }
}
