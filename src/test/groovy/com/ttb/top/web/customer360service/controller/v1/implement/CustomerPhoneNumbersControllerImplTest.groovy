package com.ttb.top.web.customer360service.controller.v1.implement

import com.ttb.top.web.customer360service.controller.v1.CustomerPhoneNumbersController
import com.ttb.top.web.customer360service.model.customerphonenumbers.request.CustomerPhoneNumbersRequest
import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersService
import jakarta.validation.Validation
import org.instancio.Instancio
import org.springframework.aop.framework.ProxyFactory
import org.springframework.validation.beanvalidation.MethodValidationInterceptor
import spock.lang.Specification
import spock.lang.Subject

class CustomerPhoneNumbersControllerImplTest extends Specification {
    def customerPhoneNumbersService = Mock(CustomerPhoneNumbersService)

    @Subject
    def customerPhoneNumbersController = new CustomerPhoneNumbersControllerImpl(customerPhoneNumbersService)

    def setup() {
        def validator = Validation.buildDefaultValidatorFactory().getValidator()
        def methodValidationInterceptor = new MethodValidationInterceptor(validator)

        def proxyFactory = new ProxyFactory(new CustomerPhoneNumbersControllerImpl(customerPhoneNumbersService))
        proxyFactory.addAdvice(methodValidationInterceptor)
        customerPhoneNumbersController = proxyFactory.getProxy() as CustomerPhoneNumbersController
    }

    def "when service return data should return success"() {
        given: "Valid request body"
        def request = Instancio.create(CustomerPhoneNumbersRequest.class)

        and: "Mock response from service"
        customerPhoneNumbersService.getCustomerPhoneNumbers(request) >> Instancio.create(CustomerPhoneNumbersResponse.class)

        when: "Call controller"
        def actual = customerPhoneNumbersController.getCustomerPhoneNumbers(request)

        then: "No exception is thrown and response is not null and success"
        noExceptionThrown()
        actual != null
        actual.isSuccess()
    }

}
