package com.ttb.top.web.customer360service.service.customerphonenumbers

import com.ttb.top.web.customer360service.constants.Constants
import com.ttb.top.web.customer360service.service.customerphonenumbers.implement.CustomerPhoneNumbersCacheServiceImpl
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.ValueOperations
import spock.lang.Specification

class CustomerPhoneNumbersCacheServiceImplTest extends Specification {
    def redisTemplate = Mock(RedisTemplate<String, Object>)
    def valueOps = Mock(ValueOperations<String, Object>)
    def customerPhoneNumberCacheServiceImpl = new CustomerPhoneNumbersCacheServiceImpl(redisTemplate)
    def uuidRegex = /^[a-f0-9\\-]{36}$/

    def "when set phone number to cache should return unique string"() {
        given:
        def staffId = "staffId"
        def phoneNumber = "1234567890"
        redisTemplate.opsForValue() >> valueOps
        when:
        def actual = customerPhoneNumberCacheServiceImpl.setCustomerPhoneNumber(staffId, phoneNumber)
        then:
        actual != null
        actual ==~ uuidRegex
        noExceptionThrown()
    }

    def "when get phone number from cache should return phone no string"() {
        given:
        def staffId = "staffId"
        def uniqueId = "1234"
        redisTemplate.opsForValue() >> valueOps
        valueOps.get(String.format("%s:staff:%s:calling_id:%s", Constants.WEB_CUSTOMER_PHONE_CACHE_VALUE, staffId, uniqueId)) >> Map.of("phoneNo", "1234567890")
        when:
        def actual = customerPhoneNumberCacheServiceImpl.getCustomerPhoneNumber(staffId, uniqueId)
        then:
        actual != null
        actual == "1234567890"
        noExceptionThrown()
    }
}
