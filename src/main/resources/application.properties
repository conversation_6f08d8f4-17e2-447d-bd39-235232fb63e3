spring.application.name=web-customer-360-service

# Feign client
spring.cloud.openfeign.okhttp.enabled=true

# lookup
lookup.type.name=top.response.status
lookup.feignexception.enabled=true

# decrypt-helper
db.encryption.aes.key=R+nddbXc87mAwUD9jvmY+f36pOoenInvEayGxqspuhE=@tZ76MfyqZbcPkNCB3WJ9ow==

# tablet RSA key
decrypt.rsa.private.key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyRrcxivnJosBTSmh4QgQplY9txAIH7FIigAhyiLCDLQtgtT5pD+11xW3RYnZwWioYQmGqkFMzigfKDOkDduvvZhA7m4+DW9ylxqGuKj644zRwzgiJ5U5qgRmHzmycj3dgiDs/7wP8BZhC48DzoMbbziCURkaFze8mUyrKsDYqqta0TYdDritVeL6Ud//D/CfZSZwOfMxy+BRNDJRADPt+msUFeu2ZBpRq8w/lPyV8vQ98fYSFqlV1N1lsiBOGEwY1ppqFZcnGXQ1K2VQvdC5jhHZyzLaor+XZt64X5OX8tsfjXaT83Th/yWuza1awzqveHZcxw7gyizTjosatlPvVAgMBAAECggEAJkumgo/2BGhfpASx2FNmDYDBJLUcMpODOUIDjobqU+NTNFz6oRr4yXm1k2rxQkU8EaYA0ODb3pBiB/cp/sKHABAOoJ9T/sW26i13AbC1dIXp9+lqUCTf6WT+FPw0vJTc8fGRuLQhSPvyrzu5cRwyW3k16mQGNiv8mWD4Kj4cBKH+V1bpUQaRClgp41ZOMVv+g6xeH9Trv41B5JT7Z2ycTvQVvSXSAtCebbfICH1ELbjfADwV8DH2wDbbrew5RxGSPiXBQcFYib9F3yoi80Zc+AsuYcQC0fNCsygkQ0wicMH8vVFKCRWL8mPQ6aUUXvslDws5JqDkUtdrQMpmpUUoaQKBgQDbzTsvNX4XVxJc36cxZtF4e5wMNNEmRXuvvukI7L/E5amBq6Rj0gRvP8KM5LnPzy4OE0mQVBpL92ppuoputYRsbFOV91AWgBAGGoFL8ho40wDLZ4HCU1Fj6Psl9O4+Rh28w8ZdmgWAvh7FGvvsNMaUL/9ewmLjhpp5f+FjYosNVwKBgQDPosjd92xNuxiDB3p5oXbFVrcqvcsBtZiWtfJftjl4SZQcKMjXqvTiY7Pqrz/SXl1ZVKKDC48cU8PrNSzBhcRwZ2IV1s+4rHbb8Dm8IWVyhTNuSn6ftDews/w5Xzgo/PazxzbXLnqq62LMdxXNjVDLAuN7NfzMmkURzFJv2KmYswKBgQCuC4aPzTW42ZOKwvYq4hV/57Ea4T+zpFVaRjtUe9Ml4A0mxnj3KbelN8GfuwV/DbiUIKWhiVcBTDqQ2cr/+u+OwwA0wY5DIsiNbLNxJZWp5Tq91YokC8Fo8XTdC2MTIIYvkH4kY+9zkBfhT4qn8OpFMPRvXlDbhRwQlTgtcDxXJQKBgBoiejf+IaKzDwXHFjJjEWkLXijCFOBVNCycIDLN4/PxBvR4abdDrGkmdYnvnw/ikstgrMfj15KQNJPRcJ23MZ+YU68+B41OH/PVC99TMMq2W1/hfoipjWzvaqrqAk6ecIr2Yz+4ePY0hI4J2zOxOt8isPFcPUKflFwGJMYxNj+jAoGAYblB6i9MIZu3bpk/WPFPHjHmtKN61pJ2fFrzXyDtk3d5S2+FkqywWCLNXkDD16b4yapOgbzVs0/yIUIO6250DihDpVvViddMAngYaQ7DWHUH5lbGRcPF8znc4XWijaYA0RaaLVhTKmr3NSXDxreOKgiNT4JdaFS7rlivFG4HXzU=
encrypt.rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAska3MYr5yaLAU0poeEIEKZWPbcQCB+xSIoAIcoiwgy0LYLU+aQ/tdcVt0WJ2cFoqGEJhqpBTM4oHygzpA3br72YQO5uPg1vcpcahrio+uOM0cM4IieVOaoEZh85snI93YIg7P+8D/AWYQuPA86DG284glEZGhc3vJlMqyrA2KqrWtE2HQ64rVXi+lHf/w/wn2UmcDnzMcvgUTQyUQAz7fprFBXrtmQaUavMP5T8lfL0PfH2EhapVdTdZbIgThhMGNaaahWXJxl0NStlUL3QuY4R2csy2qK/l2beuF+Tl/LbH412k/N04f8lrs2tWsM6r3h2XMcO4Mos046LGrZT71QIDAQAB

# web RSA key
web.decrypt.rsa.private.key=MIIJKAIBAAKCAgEAqtmCdYhOTiWFJ7ODLuL/jhkK6qaDhUfmn61tUWGPBnc4+kVnkyq2mZZJ5P8S5NGJTUWCpd0Tpm72TMN68a5ch+VQ5iMbCxjX9G8ycMvtgQjTjEHGMdnWfU6r35shH/zhaixF510cThKAynMstDVUyLTxadCoicGHLTH7tdBk78qP25g2fHqhlrZ/2Bzb229bcq8bkFovRqfN57ENOhkbzu7DGgq7ID0CaPSciBh4yPdASW6zFZeDdnC9GNLp4oP1VBhWdb6uR8A2Z9LFAeLrdJhVxRRI5Q8u//PpBPFp0Y2e9kiUVkxvQ7KsCC7l1ULdQ6pBv+R/T94Og+kp4nYjbZaPqFg2hcelLTNEdBSVCByo27L+BczIJJMhDq6U+YSHI/iWt2T/TZnGCiB496cwWvN+l9dwE4dvKp+WCCEdtVQsXDWUZbDCltfvF95L0T3JnvHz+CqmWb33U3skTr7f51FNkAGYdu7sG728VGBVHOWkban5ic8yW0etZdtkrMO7qvINpT+aaqx9YEDO31HqYUOJNb6d4cTsMAe9JlX4Kuf7BOaY6OsrlFXBAr2De+zYU72L4lIXoGD0lw3zMM2yr8B0rJuGkqhiBa7otpIqH36DgdMcpgEoNfLv+8psaKwRwRMwPo5SGGnkDsHumQINAlj8syeVfw564h1TjuvOqZsCAwEAAQKCAgASdv2VjEzCP2Xbu5gC+j+acPRwFd0DC5lOSqUU9ZFKppn+2n9uNWCUytxjpnm5RzgqpMeQ+4rDIxz1gO8JrcmMWmla5hJnKaE1Zssxy6h4mKaccwNjoeJ/moAS46Y52c/W9L/9WF/VxEQtHTOIqnbmVj/9KrL8aHEenldQ+Bvgmqjfl9zTqUFLk5/PkJbmaWudwnUdLdIMNdiYINeIHuyVedKWPvCodE+NXlqIjvSG68oD5bXvMHfh1h9pzmm+tHpQQ20cvqYBVTHMGMh4Arll2YzzhtS2BOUUfp0BjsHcBw1WGtQFW69uxqG3JLJsURSu2njOr3he/mXdNdb0BcagRNP63l+c+IfsZqs+m/oDVnkWk8sZRmZAcGjBbRgLYI27bpQGoQBnYTDQHybvsAJWpeS5hzhG5qp3RumXXHhwf4QgA8LcBBlkfMt8G6tP5A1ntwxo8VXrj1rU6IW88BTX2tHExNeS2d1nxiDezZzCDDCayIhN5VGv5Fh4IIeyd6syeuSdeSPVakVgBUI6vLy14bH9ApmuU6cI5lcyhgpyn4Ia+boMeq35L1pSg187thsnRLwu48tP7lV2FJywxNtAgIl1ZuvzD5TB05840MHpoF2oNLP5VCAAcvgsZ/9r6n2VDsg9+f/ShuvBcVCoOSf4T/tfuOPx3LXSyRs+jYCT6QKCAQEAtnk3E09E5Lt9vUmk5ww4AH41TMD0spO2ZPfXm/xjYqmFFWlVAv7YZ5leCujMmZPgZRJ76Df7ZfjmTT5SW9fpSzOSqHqIWs9OTCcC4gBt6dWj+1PkAdEqkm747g4BPY/2aehWDCndVNOVYrEBHFgbdFg+4olfBUVftoHVL+09G1Jaqc97mx++dHlkzOuHJHGH5P1pTfgl9d+ef/sj7XKFvrsXVjIgx6zeG12caoP6HJ4/+cBt9YIsNXEIGfqE++JKYECrLstfy5+P2ATKXKVIOaMikF6n6tI/6LUEzMmDyZ3E6Bf6b4y6FjZG32KggiwBKL69iUSaOrVfyy4NAD8spQKCAQEA77FA3G7ZXjMz1K/WwodtmmX1aXeLI4v5ajkyPCSyA1IulIzLRLmRXa79/DmTXQ1mxx3z9mHoPlOuImLChtxxEKXR+gEK4JRjGrEGvVrDvnSVyHdh1vld9xM9xMKLBt48427tdVRMkMw9ginnlBjGkVUu6y4nny8LUv3KiNkYJInDuCNCVkFB/bmEcoqvpQQ61P2yCNHwYnZ7uPsevFaiqzq0t6bbDRst4qaz3Jppldxq34lBHKPGu6x1CTINYrXIKXK3NaxFtbIb5vjep/5EX9Qr2j8hQC1jVkng1DkI4tUnRgvp+JyCC5mHFAXTNPKgHEQvzITEkGmdrv8ZlbdpPwKCAQEAot9FQq4Rn1yfGlLepwCoCACfEJh2keL4/dyfDfNUvp7zVC5FnDve63HgmDo9TaPJuk2qXZeChP2Ki03ic2ogKWiqMknU5cF+cMitcQ7TDNIN4Wp67QhHSJdXCB1mDA4w801XaNoBXhLRl1rI1ATs1Idj8g6NywWmLiBrKrFGRtnWZRX2jXrjI9GrIKHmaagQlVuAIAEr2PA16KRA6FO9f4Y9iUmLKs9pHOIMmKKen+a80e9p9qavUTBH+/A5vR66Jnnykf0jz3voB/gWrGU3N4MeeeCTXy01sD/LPgNs54w0Urn8Zo8lFmhyEDhXbnlau4StZxXd2CNGkvPS1PKOpQKCAQB4kJtEhcl2laC7mdsXrFFYOXLxIv1s2uqRIMYA9KBKBVuCwzM3lXPmQLBm33sdcjFBdPKS+vp751GO4Ehi151Tt2TgMQgMoPccpPDz7CTLtw7kcBBZtsMqFZabUxPlV4IfEFwHCWkd6G4D/BoNJ00yKzvXdjbd3ib/DsOChFidgGJfS/UBN//+pQNXLiVdhrwlCJs1zovwqxJeoIn8SEO4h2YBfr9uXz58ZEfSbiUwCVgEqqCCzGqZWgzgnDmZbkCJgLdhGw9Rbo6UZphwvfgcj7ejJuANzQbLj4u0lB/BIWCI3lNJB0hul0IXszpixqv8q9uzLxFEZqu3K3/uC/y3AoIBAHhUsioP/NAMO/dGIYT0X0ZE5AJtz1HG8zUQTLSyWQW/lVHgzBEbY6DwPgvWuVmzqvYI2yRJTIlMMbT0/jNcV/4VXbeb8wip/P0p7B18PN2rvbuhm+Qhrkw3hTNBxDSwvd5ShCte1Lo4IKyrkF4XOkJ14zR8SdXmCJRECQjbILIfG8fpHQGmvC8s4vJ/pus7xDY2iYlTbSRjFXtLlSxFeSrdB2PwoPK8Eyh/v3QNwwGP2kLTaLlasUBjIAopFFs5R/z1yFgh4kgaHpMLRe2PP4OccVV8qksIjP1/JOwh+cOcNPKVEmFF7/0sd1B6r48/aPTWN3nE+pr/CLVEl2NsCto=
web.encrypt.rsa.public.key=MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAw5qK6/mL9Goso04fv90EQto4PLdl6z3ZGfV7KAlelIYRHUwIkMgteBJufwdzlRUau8ADLN8iCr4SOEzvsmBE5PyOWiO5DuuZZp7PrYfu6JX5dLjVrjuS5DisHo+GtZ2ze2liLWZ0jjW9YU+PtMNOtGgZJN4nB55UCrgFfq30CxH177XYYVVwdu4cmz//4K4mXhcwO+YOXqUjIKe1jzabbkwWXPbAp7mx4FPOyoB6BaHbiRd3nUhWSNRHbEukz1qmrdPd7AWpOVF+CT50Li9N2EF+1ID1q9Bqu+QPxVXvtJwLzp69TvdYGe3AINh4DBm6VovEeeCk4B6C/EserhPqfW+8HRPjCHghgRiSSHfZxFvfbta84oz8kEras7Qy2sqmJSWe06n0c3fi0IPP3UHTBMnTl4C88yDplVN0wdLKau4ggcgThx4/VM2IUSyBQd6ulPYDCdQwb77opS6dVShhNWunKmzml3g6ypyrA15cLbTTrPK2+qJYkZZkjZjXjs7vXsOzIdoMfiaTX5CRMFs1rE3r95T9Z+x9w9s4L0+NlHLNX+dDE5L3tD744Pa1olV/ke4Ckk+Ni+cscARTjtxVAPCTPhCn/47t/6OcukH5LJAZxCwKk6Auxz9rd4IKqqJbpLXwUtQdBuzC7Xkkxq7aj/8OZWzqiAlkEg2xBl2khX0CAwEAAQ==

#request-loh-helper
masking.config.file=masking-pattern.xml

#rbac-service
feign.rbac-service.name=rbac-service
feign.rbac-service.url=http://rbac-service:8080
feign.rbac-service.permission.endpoint=/v1/rbac-service/permission
feign.rbac-service.permission.web.assetlv2.value=WEB_CUST360_PRODUCTLV2_ASSET_ACCOUNT_AMOUNT_VIEW
feign.rbac-service.permission.web.liabilitylv2.value=WEB_CUST360_PRODUCTLV2_LIABILITY_ACCOUNT_AMT_VIEW

#customer-360-service
feign.customer-360-service.name=customer-360-service
feign.customer-360-service.url=http://customer-360-service:8080
feign.customer-360-service.rbac-permission.endpoint=/v1/customer-360-service/rbac/permission
feign.customer-360-service.tag-available.endpoint=/v1/customer-360-service/tags/available
feign.customer-360-service.rbac-menu.endpoint=/v1/customer-360-service/rbac/menu/{page_name}
feign.customer-360-service.special-note-inquiry.endpoint=/v1/customer-360-service/special-note/inquiry
feign.customer-360-service.special-note-upsert.endpoint=/v1/customer-360-service/special-note/upsert
feign.customer-360-service.special-note-group-role-inquiry.endpoint=/v1/customer-360-service/special-note/group-role/inquiry


#auth-service
feign.auth-service.name=auth-service
feign.auth-service.url=http://auth-service:8080
feign.auth-service.staff-login-info.endpoint=/v1/auth-service/staff-login-info/inquiry

#biz-customer-360-service
feign.biz-customer360-service.name=biz-customer-360-service
feign.biz-customer360-service.url=http://biz-customer-360-service:8080
feign.biz-customer360-service.customer-profile-detail-inquiry.endpoint=/v1/biz-customer-360-service/customer-profile/detail/inquiry
feign.biz-customer360-service.product-asset-detail-inquiry.endpoint=/v1/biz-customer-360-service/product/asset/detail/inquiry
feign.biz-customer360-service.customer-profile-detail-inquiry.servicename=customer-profile-detail-inquiry
feign.biz-customer360-service.customer-dncs-update-dncs.endpoint=/v1/biz-customer-360-service/customers/dncs/update-dncs
feign.biz-customer360-service.customer-dncs-update-dncs.servicename=customers-dncs-update-dncs
feign.biz-customer360-service.product-liability-detail-inquiry.endpoint =/v1/biz-customer-360-service/product/liability/detail/inquiry
feign.biz-customer360-service.product-assetlv2-detail.endpoint=/v2/biz-customer-360-service/product/asset/detail/inquiry

#biz-customer-data-service
feign.biz-customer-data-service.name=biz-customer-data-service
feign.biz-customer-data-service.url=http://biz-customer-data-service:8080
feign.biz-customer-data-service.historical-case-inquiry.endpoint=/v1/biz-customer-data-service/historical-case/inquiry

#customer-data-service
feign.customer-data-service.name=customer-data-service
feign.customer-data-service.url=http://customer-data-service:8080
feign.customer-data-service.customer-profile-dipchip-image.endpoint=/v1/customer-data-service/customer-profile/dipchip-image
feign.customer-data-service.ttbtouch-customer-status.endpoint=/v1/customer-data-service/ttbtouch/customer-status
feign.customer-data-service.historical-case.advance-search-lov-inquiry.endpoint=/v1/customer-data-service/common-lov/historical-case/inquiry
feign.customer-data-service.case-complain.inquiry.endpoint=/v1/customer-data-service/case-complain/inquiry
feign.customer-data-service.consents-consents.pdpa.inquiry.endpoint=/v1/customer-data-service/customer-consents/pdpa/inquiry
customer-data-service.case-complain.inquiry.limit-rows=5
feign.customer-data-service.promptpay-mobile.endpoint=/v1/customer-data-service/promptpay/mobile/inquiry
feign.customer-data-service.promptpay-mobile.servicename=promptpay-mobile
feign.customer-data-service.promptpay-citizen.endpoint=/v1/customer-data-service/promptpay/citizen/inquiry
feign.customer-data-service.promptpay-citizen.servicename=promptpay-citizen
feign.customer-data-service.customer-consents.tax.inquiry.endpoint=/v1/customer-data-service/customer-consents/tax/inquiry
feign.customer-data-service.dncs.endpoint=/v1/customer-data-service/customers/dncs/inquiry
feign.customer-data-service.crm.historical-case-ntb.endpoint=/v1/customer-data-service/crm/historical-case-ntb/inquiry
feign.customer-data-service.phone-numbers.endpoint=/v1/customer-data-service/customer-profile/get-customer

#customer-product-service
feign.customer-product-service.name=customer-product-service
feign.customer-product-service.url=http://customer-product-service:8080
feign.customer-product-service.bank-service-status-inquiry.endpoint=/v1/customer-product-service/bank-service-status/inquiry
feign.customer-product-service.loan.accountdetail.inquiry.endpoint=/v1/customer-product-service/loan/account-detail/inquiry
feign.customer-product-service.product-master.inquiry.endpoint=/v1/customer-product-service/product-master/inquiry

#permissions
#permissions.url.web-customer-profile.highlight=/v1/web-customer-360-service/customer-profile/highlight/inquiry
#permissions.page.web-customer-profile.highlight=WEB360LANDINGTESET
#permissions.section.web-customer-profile.highlight=CUST360_PERSONAL_INFO

#permissions.url.web-customer-profile.tag=/v1/web-customer-360-service/tag/inquiry
#permissions.page.web-customer-profile.tag=WEB360LANDING
#permissions.section.web-customer-profile.tag=CUST360_PERSONAL_INFO

#POST /v1/web-customer-360-service/special-note/upsert
#permissions.url.customer-special-note.upsert=/v1/web-customer-360-service/special-note/upsert
#permissions.page.customer-special-note.upsert=WEB360LANDING
#permissions.section.customer-special-note.upsert=SPECIAL_NOTE

#POST /v1/web-customer-360-service/customer-consents/pdpa/inquiry
#permissions.url.customer-consents-pdpa-inquiry=/v1/web-customer-360-service/customer-consents/pdpa/inquiry
#permissions.page.customer-consents-pdpa-inquiry=WEB360CUSTDETAILLV2
#permissions.section.customer-consents-pdpa-inquiry=WEB360_CUSTDETAILLV2_VIEW

#POST /v1/web-customer-360-service/product/asset/detail/inquiry
#permissions.url.customer-product-asset-detail-inquiry=/v1/web-customer-360-service/product/asset/detail/inquiry
#permissions.page.customer-product-asset-detail-inquiry=WEB360PRODUCTLV2
#permissions.section.customer-product-asset-detail-inquiry=WEB360_PRODUCTLV2_AUM_VIEW
#permissions.section.customer-product-asset-detail-inquiry=WEB360_PRODUCTLV2_ASSET_ACCOUNT_VIEW

#POST /v1/web-customer-360-service/customer-phone-numbers
#permissions.url.customer-phone-number=/v1/web-customer-360-service/customer-phone-numbers
#permissions.page.customer-phone-number=WEB360LANDING
#permissions.section.customer-phone-number=ICON_CLICK_TO_CALL

app.api.logging.max-length=3000

# Feign Client Logger Level
feign.client.config.default.loggerLevel=FULL

#cache
cache.customer360.expire-seconds.web-customer-360-tag-info-crm=3600
cache.web-customer360.expire-seconds.customer-360-get-customer=1800


#activity-log.customer-360-service.special-note-upsert.flow=Desktop Mode Cust 360 - Landing Page
#activity-log.customer-360-service.special-note-upsert.step=Save Special Note to Database
#activity-log.customer-360-service.special-note-upsert.staff-act-log-id=03200

# activity log
activity-log.topic=activity-logging-${env}
activity-log.eventhub.bootstrap-servers=${env.eventhub.servers}
activity-log.eventhub.security.protocol=${env.eventhub.protocol}
activity-log.eventhub.password=${env.eventhub.password}
activity-log.eventhub.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="${activity-log.eventhub.password}";
activity-log.eventhub.sasl.mechanism=${env.eventhub.sasl.mechanism}
activity-log.customer-360-service.special-note-upsert.flow=Desktop Mode Cust 360 - Landing Page
activity-log.customer-360-service.special-note-upsert.step=Save Special Note to Database
activity-log.customer-360-service.special-note-upsert.staff-act-log-id=03200
env.decrypt.rsa.private.key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyRrcxivnJosBTSmh4QgQplY9txAIH7FIigAhyiLCDLQtgtT5pD+11xW3RYnZwWioYQmGqkFMzigfKDOkDduvvZhA7m4+DW9ylxqGuKj644zRwzgiJ5U5qgRmHzmycj3dgiDs/7wP8BZhC48DzoMbbziCURkaFze8mUyrKsDYqqta0TYdDritVeL6Ud//D/CfZSZwOfMxy+BRNDJRADPt+msUFeu2ZBpRq8w/lPyV8vQ98fYSFqlV1N1lsiBOGEwY1ppqFZcnGXQ1K2VQvdC5jhHZyzLaor+XZt64X5OX8tsfjXaT83Th/yWuza1awzqveHZcxw7gyizTjosatlPvVAgMBAAECggEAJkumgo/2BGhfpASx2FNmDYDBJLUcMpODOUIDjobqU+NTNFz6oRr4yXm1k2rxQkU8EaYA0ODb3pBiB/cp/sKHABAOoJ9T/sW26i13AbC1dIXp9+lqUCTf6WT+FPw0vJTc8fGRuLQhSPvyrzu5cRwyW3k16mQGNiv8mWD4Kj4cBKH+V1bpUQaRClgp41ZOMVv+g6xeH9Trv41B5JT7Z2ycTvQVvSXSAtCebbfICH1ELbjfADwV8DH2wDbbrew5RxGSPiXBQcFYib9F3yoi80Zc+AsuYcQC0fNCsygkQ0wicMH8vVFKCRWL8mPQ6aUUXvslDws5JqDkUtdrQMpmpUUoaQKBgQDbzTsvNX4XVxJc36cxZtF4e5wMNNEmRXuvvukI7L/E5amBq6Rj0gRvP8KM5LnPzy4OE0mQVBpL92ppuoputYRsbFOV91AWgBAGGoFL8ho40wDLZ4HCU1Fj6Psl9O4+Rh28w8ZdmgWAvh7FGvvsNMaUL/9ewmLjhpp5f+FjYosNVwKBgQDPosjd92xNuxiDB3p5oXbFVrcqvcsBtZiWtfJftjl4SZQcKMjXqvTiY7Pqrz/SXl1ZVKKDC48cU8PrNSzBhcRwZ2IV1s+4rHbb8Dm8IWVyhTNuSn6ftDews/w5Xzgo/PazxzbXLnqq62LMdxXNjVDLAuN7NfzMmkURzFJv2KmYswKBgQCuC4aPzTW42ZOKwvYq4hV/57Ea4T+zpFVaRjtUe9Ml4A0mxnj3KbelN8GfuwV/DbiUIKWhiVcBTDqQ2cr/+u+OwwA0wY5DIsiNbLNxJZWp5Tq91YokC8Fo8XTdC2MTIIYvkH4kY+9zkBfhT4qn8OpFMPRvXlDbhRwQlTgtcDxXJQKBgBoiejf+IaKzDwXHFjJjEWkLXijCFOBVNCycIDLN4/PxBvR4abdDrGkmdYnvnw/ikstgrMfj15KQNJPRcJ23MZ+YU68+B41OH/PVC99TMMq2W1/hfoipjWzvaqrqAk6ecIr2Yz+4ePY0hI4J2zOxOt8isPFcPUKflFwGJMYxNj+jAoGAYblB6i9MIZu3bpk/WPFPHjHmtKN61pJ2fFrzXyDtk3d5S2+FkqywWCLNXkDD16b4yapOgbzVs0/yIUIO6250DihDpVvViddMAngYaQ7DWHUH5lbGRcPF8znc4XWijaYA0RaaLVhTKmr3NSXDxreOKgiNT4JdaFS7rlivFG4HXzU=
env.encrypt.rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAska3MYr5yaLAU0poeEIEKZWPbcQCB+xSIoAIcoiwgy0LYLU+aQ/tdcVt0WJ2cFoqGEJhqpBTM4oHygzpA3br72YQO5uPg1vcpcahrio+uOM0cM4IieVOaoEZh85snI93YIg7P+8D/AWYQuPA86DG284glEZGhc3vJlMqyrA2KqrWtE2HQ64rVXi+lHf/w/wn2UmcDnzMcvgUTQyUQAz7fprFBXrtmQaUavMP5T8lfL0PfH2EhapVdTdZbIgThhMGNaaahWXJxl0NStlUL3QuY4R2csy2qK/l2beuF+Tl/LbH412k/N04f8lrs2tWsM6r3h2XMcO4Mos046LGrZT71QIDAQAB
spring.autoconfigure.exclude=org.springframework.boot.actuate.autoconfigure.metrics.cache.CacheMetricsAutoConfiguration
env=

# Eventhub
env.eventhub.host=evh-a0551-top-dev.servicebus.windows.net
env.eventhub.port=9093
env.eventhub.access.key=58iSGMSgh2+NMQrEfTtBbtd2xyeZ1T3YC+AEhD51UAU=
env.eventhub.servers=${env.eventhub.host}:${env.eventhub.port}
env.eventhub.password=Endpoint=sb://${env.eventhub.host}/;SharedAccessKeyName=AdminSharedAccessKey;SharedAccessKey=${env.eventhub.access.key}
env.eventhub.protocol=SASL_SSL
env.eventhub.sasl.mechanism=PLAIN
dmn.root.path=classpath:dmn
ruleActivityLogCancelTask.dmn.file.path=${dmn.root.path}/rule_activity_log_cancel_task.dmn

# Advance Search
customer-data-service.advance-search-lov.inquiry.type=serviceCategory,status,dataSource

# Countdown seconds for aum inquiry
refresh.countDown.sec=300