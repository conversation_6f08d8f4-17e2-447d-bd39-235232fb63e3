package com.ttb.top.web.customer360service.controller.v1.implement;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.web.customer360service.controller.v1.HistoricalCaseController;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseLandingRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseTouchPointHistoryRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.Landing360NtbRequest;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLanding360NtbResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLandingResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseTouchPointHistoryResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.Type;
import com.ttb.top.web.customer360service.service.historicalcase.HistoricalCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class HistoricalCaseControllerImpl implements HistoricalCaseController {

    private final HistoricalCaseService historicalCaseService;

    @Override
    public ResponseModel<HistoricalCaseLandingResponse> getHistoricalCaseLanding(HistoricalCaseLandingRequest request) {
        return ResponseModel.success(historicalCaseService.getHistoricalCaseLanding(request));
    }

    @Override
    public ResponseModel<Map<String, List<Type>>> inquiryAdvanceSearchLov(String staffId) {
        log.info("Start web-customer-360-service /historical-case/advance-search/lov");
        return ResponseModel.success(historicalCaseService.inquiryAdvanceSearchLov());
    }

    @Override
    public ResponseModel<HistoricalCaseTouchPointHistoryResponse> getHistoricalCaseTouchPointHistory(
            HistoricalCaseTouchPointHistoryRequest request
    ) {
        return ResponseModel.success(historicalCaseService.getHistoricalCaseTouchPointInquiry(request));
    }

    @Override
    public ResponseModel<List<HistoricalCaseLanding360NtbResponse>> getLanding360NTB(Landing360NtbRequest request) {
        return ResponseModel.success(historicalCaseService.inquiryLanding360Ntb(request));
    }
}
