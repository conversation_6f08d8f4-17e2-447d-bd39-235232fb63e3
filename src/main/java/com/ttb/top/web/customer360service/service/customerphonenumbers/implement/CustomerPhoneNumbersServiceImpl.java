package com.ttb.top.web.customer360service.service.customerphonenumbers.implement;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.web.customer360service.feign.CustomerDataServiceFeignClient;
import com.ttb.top.web.customer360service.mapper.CustomerProfilePhoneNumberMapper;
import com.ttb.top.web.customer360service.model.customerphonenumbers.request.CustomerPhoneNumbersRequest;
import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomerProfileCustomerRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerProfileCustomerResponse;
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersCacheService;
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersService;
import com.ttb.top.web.customer360service.utils.ExceptionUtil;
import com.ttb.top.web.customer360service.utils.LogExecutionTime;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant.STAFF_ID;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerPhoneNumbersServiceImpl implements CustomerPhoneNumbersService {

    private final HttpHeaders httpHeaders;
    private final CustomerDataServiceFeignClient customerDataServiceFeignClient;
    private final CustomerPhoneNumbersCacheService customerPhoneNumbersCacheService;

    @LogExecutionTime
    @Override
    public CustomerPhoneNumbersResponse getCustomerPhoneNumbers(CustomerPhoneNumbersRequest request) {
        try {
            validateHeaders();
            validateRequestBody(request);
            FeignCustomerProfileCustomerResponse feignCustomerProfileCustomerResponse = fetchCustomerProfileCustomer(
                request);
            return CustomerProfilePhoneNumberMapper
                .INSTANCE
                .mapCustomerProfilePhoneResponseToCustomerPhoneResponseNumbers(
                    feignCustomerProfileCustomerResponse, customerPhoneNumbersCacheService,
                    httpHeaders.getFirst(STAFF_ID));
        } catch (GenericException | FeignException e) {
            log.error("Error getCustomerPhoneNumbers : ", e);
            if (ExceptionUtil.isTimeoutException(e)) {
                throw new GenericException(ResponseCodeEnum.INTERNAL_SERVER_TIMEOUT_ERROR_CODE);
            }
            throw e;
        } catch (Exception e) {
            log.error("Error getCustomerPhoneNumbers : ", e);
            throw new GenericException();
        }
    }

    FeignCustomerProfileCustomerResponse fetchCustomerProfileCustomer(
        CustomerPhoneNumbersRequest request
    ) {
        FeignCustomerProfileCustomerRequest feignCustomerProfileCustomerRequest
            = FeignCustomerProfileCustomerRequest.builder()
            .rmId(request.getRmId()).ecId(request.getEcId())
            .build();

        log.info("Calling POST /v1/customer-data-service/customer-profile/get-customer");

        ResponseModel<FeignCustomerProfileCustomerResponse> feignCustomerProfileCustomerResponse =
            customerDataServiceFeignClient.getPhoneNumbers(httpHeaders, feignCustomerProfileCustomerRequest);
        if (feignCustomerProfileCustomerResponse.getDataObj() == null) {
            throw new GenericException(ResponseCodeEnum.DATA_NOT_FOUND);
        }
        return feignCustomerProfileCustomerResponse.getDataObj();
    }

    void validateRequestBody(CustomerPhoneNumbersRequest request) {
        boolean isRmIdExist = StringUtils.isNotBlank(request.getRmId());
        boolean isEcIdExist = StringUtils.isNotBlank(request.getEcId());
        if (isRmIdExist == isEcIdExist) {
            log.error("Missing required RmId or EcId in request body");
            throw new BadRequestException();
        }
    }

    void validateHeaders() {
        if (StringUtils.isEmpty(httpHeaders.getFirst(STAFF_ID))) {
            log.error("Missing required STAFF-ID in headers");
            throw new BadRequestException();
        }
    }
}
