package com.ttb.top.web.customer360service.mapper;

import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignHistoricalCaseNtbResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLanding360NtbResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface HistoricalCaseNtpMapper {
    HistoricalCaseNtpMapper INSTANCE = Mappers.getMapper(HistoricalCaseNtpMapper.class);

    HistoricalCaseLanding360NtbResponse mapToLanding360NtbResponse(
            FeignHistoricalCaseNtbResponse feignHistoricalCaseNtbResponse);

    List<HistoricalCaseLanding360NtbResponse> toLanding360NtbResponseList(
            List<FeignHistoricalCaseNtbResponse> feignHistoricalCaseNtbResponseList);
}

