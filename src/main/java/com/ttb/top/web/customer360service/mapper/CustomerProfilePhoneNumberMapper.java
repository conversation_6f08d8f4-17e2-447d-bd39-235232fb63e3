package com.ttb.top.web.customer360service.mapper;

import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerProfileCustomerResponse;
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersCacheService;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Mapper(componentModel = "spring")
public interface CustomerProfilePhoneNumberMapper {

    CustomerProfilePhoneNumberMapper INSTANCE = Mappers.getMapper(CustomerProfilePhoneNumberMapper.class);
    String PHONE_M = "M";
    String PHONE_B = "B";
    String PHONE_R = "R";
    int FIRST_INDEX = 0;
    DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    @Mapping(target = "phonesM", source = "response", qualifiedByName = "mapPhonesM")
    @Mapping(target = "phonesB", source = "response", qualifiedByName = "mapPhonesB")
    @Mapping(target = "phonesR", source = "response", qualifiedByName = "mapPhonesR")
    CustomerPhoneNumbersResponse mapCustomerProfilePhoneResponseToCustomerPhoneResponseNumbers(
        FeignCustomerProfileCustomerResponse response,
        @Context CustomerPhoneNumbersCacheService customerPhoneNumbersCacheService,
        @Context String staffId
    );

    @Named("mapPhonesM")
    default CustomerPhoneNumbersResponse.Phone mapPhonesM(
        FeignCustomerProfileCustomerResponse response
    ) {
        return phone(response, PHONE_M);
    }

    @Named("mapPhonesB")
    default CustomerPhoneNumbersResponse.Phone mapPhonesB(
        FeignCustomerProfileCustomerResponse response
    ) {
        return phone(response, PHONE_B);
    }

    @Named("mapPhonesR")
    default CustomerPhoneNumbersResponse.Phone mapPhonesR(
        FeignCustomerProfileCustomerResponse response
    ) {
        return phone(response, PHONE_R);
    }

    @Mapping(target = "phoneNo", source = "feignPhoneResponse.phoneNo")
    @Mapping(target = "phoneNoTo", source = "feignPhoneResponse.phoneNoTo")
    @Mapping(target = "identPhone", source = "feignPhoneResponse.identPhone")
    @Mapping(target = "phoneNoExt", source = "feignPhoneResponse.phoneNoExt")
    @Mapping(target = "phoneNoFull", source = "feignPhoneResponse.phoneNoFull")
    @Mapping(target = "phonePrefix", source = "feignPhoneResponse.phonePrefix")
    @Mapping(target = "phoneType", source = "feignPhoneResponse.phoneType")
    @Mapping(target = "createDate", source = "feignPhoneResponse.createDate")
    @Mapping(target = "createBy", source = "feignPhoneResponse.createBy")
    @Mapping(target = "updateDate", source = "feignPhoneResponse.updateDate")
    @Mapping(target = "updateBy", source = "feignPhoneResponse.updateBy")
    CustomerPhoneNumbersResponse.Phone mapFeignPhoneResponseToCustomerPhoneResponse(
        FeignCustomerProfileCustomerResponse.Customer.Phone feignPhoneResponse
    );


    default CustomerPhoneNumbersResponse.Phone phone(FeignCustomerProfileCustomerResponse response, String phoneType) {
        if (response != null) {
            List<FeignCustomerProfileCustomerResponse.Customer.Phone> phoneMList = new ArrayList<>(
                response.getCustomer().getPhones().stream().filter(
                        phone -> StringUtils.isNotBlank(phone.getPhoneNo()) && phone.getPhoneType().equals(phoneType))
                    .toList());
            phoneMList.sort(Comparator.comparing(phone -> {
                FeignCustomerProfileCustomerResponse.Customer.Phone phoneRes
                    = (FeignCustomerProfileCustomerResponse.Customer.Phone) phone;
                return LocalDate.parse(phoneRes.getUpdateDate(), YYYY_MM_DD_HH_MM_SS);
            }).thenComparing(phone -> {
                FeignCustomerProfileCustomerResponse.Customer.Phone phoneRes
                    = (FeignCustomerProfileCustomerResponse.Customer.Phone) phone;
                return phoneRes.getPhoneSeq();
            }).reversed());
            if (!phoneMList.isEmpty()) {
                FeignCustomerProfileCustomerResponse.Customer.Phone phoneMResponse = phoneMList.get(FIRST_INDEX);
                return mapFeignPhoneResponseToCustomerPhoneResponse(phoneMResponse);
            }
        }
        return null;
    }

    @AfterMapping
    default void modifyCustomerResponsePostMapping(
        @MappingTarget CustomerPhoneNumbersResponse customerPhoneNumbersResponse,
        @Context CustomerPhoneNumbersCacheService customerPhoneNumbersCacheService, @Context String staffId) {
        if (customerPhoneNumbersResponse.getPhonesM() != null) {
            setPhoneSeqToCache(customerPhoneNumbersResponse.getPhonesM(), customerPhoneNumbersCacheService, staffId);
        }
        if (customerPhoneNumbersResponse.getPhonesB() != null) {
            setPhoneSeqToCache(customerPhoneNumbersResponse.getPhonesB(), customerPhoneNumbersCacheService, staffId);
        }
        if (customerPhoneNumbersResponse.getPhonesR() != null) {
            setPhoneSeqToCache(customerPhoneNumbersResponse.getPhonesR(), customerPhoneNumbersCacheService, staffId);
        }
    }

    default void setPhoneSeqToCache(CustomerPhoneNumbersResponse.Phone phone,
        CustomerPhoneNumbersCacheService customerPhoneNumbersCacheService, String staffId) {
        String phoneUniqueSeq = customerPhoneNumbersCacheService.setCustomerPhoneNumber(staffId, phone.getPhoneNo());
        phone.setPhoneSeq(phoneUniqueSeq);
    }

}
