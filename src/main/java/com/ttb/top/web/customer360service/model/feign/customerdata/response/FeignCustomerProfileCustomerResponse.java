package com.ttb.top.web.customer360service.model.feign.customerdata.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class FeignCustomerProfileCustomerResponse {
    private Customer customer;

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Customer {
        private List<Phone> phones;

        @Getter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Phone {
            private String phoneType;
            private String phonePrefix;
            private String phoneNo;
            private String phoneNoFull;
            private String phoneNoTo;
            private String phoneNoExt;
            private String createBy;
            private String identPhone;
            private int phoneSeq;
            private String updateBy;
            private String createDate;
            private String updateDate;
        }
    }
}
