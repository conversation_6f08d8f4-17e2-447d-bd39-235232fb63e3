package com.ttb.top.web.customer360service.constants;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {
    public static final String PERMISSION_PREFIX = "WEB_CUST360";
    public static final String PROFILE_DETAIL_TYPE = "Profile";
    public static final String PROFILE_HIGHLIGHT_TYPE = "Highlight";
    public static final String RM_ID = "rm-id";
    public static final String CUSTOMER_ID = "customer-id";
    public static final String ONE_MILLION_BAHT_NUMBER = "1000000";
    public static final String BAHT = "บาท";
    public static final String MILLION_BAHT = "ล้านบาท";
    public static final String KYC = "KYC";

    /*header*/
    public static final String CN_ROLE_DEFAULT = "default";

    /*Tag Type*/
    public static final String PROFILE_HILIGHT_TYPE = "Highlight";
    public static final String IAL_TAG = "ial";
    public static final String RISK_TAG = "risk";
    public static final String RISK_KYC = "kyc";
    public static final String DNCS_TAG = "dncs";
    public static final String TTB_TAG = "ttbTouch";
    public static final String CUSTTYPE_TAG = "custType";
    public static final String LYTTIER_TAG = "lytTier";
    public static final String MIB_BANKING_SERVICE = "MIB";

    /* Cache Value */
    public static final String REDIS_CUSTOM_CACHE_MANAGER_NAME = "customRedisCacheManager";
    public static final String WEB_CUSTOMER_TAG_CACHE_VALUE = "web-customer-360-tag-info-crm";
    public static final String WEB_CUSTOMER_PHONE_CACHE_VALUE = "customer_360";

    /*Object mapper*/
    public static final ObjectMapper OBJECT_MAPPER = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .build();
}