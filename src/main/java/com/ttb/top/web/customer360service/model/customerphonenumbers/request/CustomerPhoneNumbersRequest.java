package com.ttb.top.web.customer360service.model.customerphonenumbers.request;

import com.ttb.top.library.decryption.annotation.Encrypted;
import com.ttb.top.library.decryption.model.CryptSupported;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerPhoneNumbersRequest implements CryptSupported {
    @Encrypted
    private String rmId;
    @Encrypted
    private String ecId;
}
