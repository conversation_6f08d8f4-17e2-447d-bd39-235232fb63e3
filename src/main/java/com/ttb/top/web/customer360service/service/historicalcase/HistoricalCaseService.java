package com.ttb.top.web.customer360service.service.historicalcase;

import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseLandingRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseTouchPointHistoryRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.Landing360NtbRequest;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLanding360NtbResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLandingResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseTouchPointHistoryResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.Type;

import java.util.List;
import java.util.Map;

public interface HistoricalCaseService {
    HistoricalCaseLandingResponse getHistoricalCaseLanding(HistoricalCaseLandingRequest request);

    HistoricalCaseTouchPointHistoryResponse getHistoricalCaseTouchPointInquiry(
            HistoricalCaseTouchPointHistoryRequest request);

    Map<String, List<Type>> inquiryAdvanceSearchLov();

    List<HistoricalCaseLanding360NtbResponse> inquiryLanding360Ntb(Landing360NtbRequest request);
}
