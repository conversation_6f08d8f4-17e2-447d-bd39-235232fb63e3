package com.ttb.top.web.customer360service.service.customerphonenumbers.implement;

import com.ttb.top.web.customer360service.constants.Constants;
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.UUID;


@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerPhoneNumbersCacheServiceImpl implements CustomerPhoneNumbersCacheService {

    static final String PHONE_NO = "phoneNo";
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${cache.web-customer360.expire-seconds.customer-360-get-customer}")
    private int phoneNumberTtl;

    private String getCacheKey(String staffId, String uniqueRefId) {
        return String.format("%s:staff:%s:calling_id:%s", Constants.WEB_CUSTOMER_PHONE_CACHE_VALUE, staffId,
            uniqueRefId);
    }

    @Override
    public String getCustomerPhoneNumber(String staffId, String uniqueRefId) {
        Object cacheValue = redisTemplate.opsForValue().get(getCacheKey(staffId, uniqueRefId));
        if (cacheValue instanceof Map) {
            return (String) ((Map<?, ?>) cacheValue).get(PHONE_NO);
        }
        return null;
    }

    @Override
    public String setCustomerPhoneNumber(String staffId, String phoneNumber) {
        String uniqueSeq = UUID.randomUUID().toString();
        String key = getCacheKey(staffId, uniqueSeq);
        redisTemplate.opsForValue().set(key, Map.of(PHONE_NO, phoneNumber), Duration.ofSeconds(phoneNumberTtl));
        return uniqueSeq;
    }
}
