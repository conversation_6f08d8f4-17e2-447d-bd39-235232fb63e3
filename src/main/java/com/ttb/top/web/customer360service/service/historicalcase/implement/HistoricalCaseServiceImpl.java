package com.ttb.top.web.customer360service.service.historicalcase.implement;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.web.customer360service.feign.BizCustomerDataServiceFeignClient;
import com.ttb.top.web.customer360service.feign.CustomerDataServiceFeignClient;
import com.ttb.top.web.customer360service.mapper.HistoricalCaseMapper;
import com.ttb.top.web.customer360service.mapper.HistoricalCaseNtpMapper;
import com.ttb.top.web.customer360service.model.feign.bizcustomerdata.request.FeignHistoricalCaseRequest;
import com.ttb.top.web.customer360service.model.feign.bizcustomerdata.response.FeignHistoricalCaseResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignHistoricalCaseNtbRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignInquiryHistoricalCasesRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignHistoricalCaseNtbResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignInquiryHistoricalCasesResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.Lov;
import com.ttb.top.web.customer360service.model.historicalcase.request.CreateDate;
import com.ttb.top.web.customer360service.model.historicalcase.request.Filter;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseLandingRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseTouchPointHistoryRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.Landing360NtbRequest;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLanding360NtbResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLandingResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseTouchPointHistoryResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.Type;
import com.ttb.top.web.customer360service.service.historicalcase.HistoricalCaseService;
import com.ttb.top.web.customer360service.utils.DateFormatterUtil;
import com.ttb.top.web.customer360service.utils.ExceptionUtil;
import com.ttb.top.web.customer360service.utils.LogExecutionTime;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant.STAFF_ID;
import static com.ttb.top.library.utilityhelper.util.CommonUtil.unwrapResponseModel;

@Slf4j
@Service
@RequiredArgsConstructor
public class HistoricalCaseServiceImpl implements HistoricalCaseService {
    private final HttpHeaders httpHeaders;
    private final BizCustomerDataServiceFeignClient bizCustomerDataServiceFeignClient;
    private final CustomerDataServiceFeignClient customerDataServiceFeignClient;
    private final HistoricalCaseNtpMapper historicalCaseNtpMapper;

    @Value("${customer-data-service.advance-search-lov.inquiry.type}")
    private String advanceSearchLovInquiryType;

    @LogExecutionTime
    @Override
    public HistoricalCaseLandingResponse getHistoricalCaseLanding(HistoricalCaseLandingRequest request) {
        try {
            validateHeaders(httpHeaders);
            FeignHistoricalCaseRequest feignHistoricalCaseRequest = FeignHistoricalCaseRequest.builder()
                    .rmId(request.getRmId())
                    .ecId(request.getEcId())
                    .rowPerPage(request.getRowPerPage())
                    .currentPage(request.getCurrentPage())
                    .useCache(request.getUseCache())
                    .build();
            FeignHistoricalCaseResponse feignHistoricalCaseResponse = fetchHistoricalCase(feignHistoricalCaseRequest);
            return HistoricalCaseMapper.INSTANCE.mapToHistoricalCase(feignHistoricalCaseResponse);

        } catch (GenericException | FeignException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error in getHistoricalCaseLanding: ", e);
            throw new GenericException();
        }
    }

    @LogExecutionTime
    @Override
    public Map<String, List<Type>> inquiryAdvanceSearchLov() {
        FeignInquiryHistoricalCasesRequest feignInquiryHistoricalCasesRequest = FeignInquiryHistoricalCasesRequest
                .builder().type(advanceSearchLovInquiryType.split(",")).build();
        List<Lov> lovList = inquiryHistoricalCasesRequest(feignInquiryHistoricalCasesRequest).getLovList();
        if (CollectionUtils.isEmpty(lovList)) {
            return null;
        }
        return lovList.stream().collect(Collectors.groupingBy(
                        Lov::getType,
                        Collectors.mapping(
                                item -> new Type(item.getCode(), item.getValueEn(), item.getValueTh()),
                                Collectors.toList()
                        )
                )
        );
    }

    @LogExecutionTime
    @Override
    public List<HistoricalCaseLanding360NtbResponse> inquiryLanding360Ntb(Landing360NtbRequest request) {
        try {
            validateHeaders(httpHeaders);

            FeignHistoricalCaseNtbRequest feignHistoricalCaseNtbRequest = FeignHistoricalCaseNtbRequest.builder()
                            .contactPersonName(request.getContactPersonName())
                            .contactPhone(request.getContactPhone())
                            .build();

            List<FeignHistoricalCaseNtbResponse> feignHistoricalCaseNtbResponseList =
                    fetchHistoricalCaseNtb(feignHistoricalCaseNtbRequest);

            return HistoricalCaseNtpMapper.INSTANCE.toLanding360NtbResponseList(feignHistoricalCaseNtbResponseList);

        } catch (GenericException | FeignException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error in getHistoricalCaseNtbLanding: ", e);
            throw new GenericException();
        }
    }

    @LogExecutionTime
    @Override
    public HistoricalCaseTouchPointHistoryResponse getHistoricalCaseTouchPointInquiry(
            HistoricalCaseTouchPointHistoryRequest request) {
        try {
            validateHeaders(httpHeaders);
            FeignHistoricalCaseRequest.FeignHistoricalCaseRequestBuilder builder =
                    FeignHistoricalCaseRequest.builder()
                    .rmId(request.getRmId())
                    .ecId(request.getEcId())
                    .rowPerPage(request.getRowPerPage())
                    .currentPage(request.getCurrentPage())
                    .useCache(request.getUseCache());

            builder.syncDate(request.getSyncDate() == null
                    ? null : request.getSyncDate());
            builder.sortBy(request.getSortBy() == null
                    ? null : request.getSortBy());
            CreateDate createDate = CreateDate.builder()
                        .endDate(null)
                        .startDate(null)
                        .build();

            if (request.getFilter() != null && request.getFilter().getCreateDate() != null) {
                String startDateInput = request.getFilter().getCreateDate().getStartDate();
                String endDateInput = request.getFilter().getCreateDate().getEndDate();

                if (StringUtils.isNotBlank(startDateInput) && StringUtils.isNotBlank(endDateInput)) {
                    LocalDate startDate = LocalDate.parse(startDateInput);
                    LocalDate endDate = LocalDate.parse(endDateInput);
                    if (endDate.isBefore(startDate)) {
                        throw new GenericException(ResponseCodeEnum.BAD_RESPONSE_CODE);
                    }
                }

                String startDateFeign = StringUtils.isNotBlank(startDateInput)
                        ? DateFormatterUtil.stringToDateTimeStartOfDay(startDateInput)
                        : null;

                String endDateFeign = StringUtils.isNotBlank(endDateInput)
                        ? DateFormatterUtil.stringToDateTimeEndOfDay(endDateInput)
                        : null;

                createDate = CreateDate.builder()
                        .startDate(startDateFeign)
                        .endDate(endDateFeign)
                        .build();
            }


            Filter updateFilter = Filter.builder()
                        .serviceCategoryCode(request.getFilter() == null
                                ? null : request.getFilter().getServiceCategoryCode())
                        .dataSourceCode(request.getFilter() == null
                                ? null : request.getFilter().getDataSourceCode())
                        .caseStatusCode(request.getFilter() == null
                                ? null : request.getFilter().getCaseStatusCode())
                        .issueTh(request.getFilter() == null
                                ? null : request.getFilter().getIssueTh())
                        .createDate(createDate)
                        .build();

            builder.filter(updateFilter);
            FeignHistoricalCaseRequest feignHistoricalCaseRequest = builder.build();
            FeignHistoricalCaseResponse feignHistoricalCaseResponse = fetchHistoricalCase(feignHistoricalCaseRequest);

            return HistoricalCaseMapper.INSTANCE.mapToHistoricalCaseTouchPointInquiry(feignHistoricalCaseResponse);


        } catch (DateTimeParseException e) {
            log.error("Invalid date format in createDate: {}", e.getMessage());
            throw new GenericException(ResponseCodeEnum.BAD_RESPONSE_CODE);
        } catch (GenericException | FeignException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error in get Historical case touch point inquiry: ", e);
            throw new GenericException();
        }
    }

    private void validateHeaders(HttpHeaders headers) {
        if (StringUtils.isEmpty(headers.getFirst(STAFF_ID))) {
            log.error("Missing required STAFF-ID in headers");
            throw new BadRequestException();
        }
    }

    private FeignHistoricalCaseResponse fetchHistoricalCase(FeignHistoricalCaseRequest request) {
        log.info("Calling POST /v1/biz-customer-data-service/historical-case/inquiry");
        ResponseModel<FeignHistoricalCaseResponse> feignHistoricalCaseResponse =
                bizCustomerDataServiceFeignClient.getHistoricalCaseInquiry(httpHeaders, request);
        return feignHistoricalCaseResponse.getDataObj();
    }

    private List<FeignHistoricalCaseNtbResponse> fetchHistoricalCaseNtb(FeignHistoricalCaseNtbRequest request) {
        log.info("Calling POST /v1/customer-data-service/crm/historical-case-ntb/inquiry");
        ResponseModel<List<FeignHistoricalCaseNtbResponse>> feignHistoricalCaseNtbResponse =
                customerDataServiceFeignClient.getHistoricalCaseNtb(httpHeaders, request);
        return feignHistoricalCaseNtbResponse.getDataObj();
    }

    private FeignInquiryHistoricalCasesResponse inquiryHistoricalCasesRequest(
            FeignInquiryHistoricalCasesRequest request
    ) {
        try {
            log.info("Calling POST /v1/customer-data-service/common-lov/historical-cases/inquiry");
            return unwrapResponseModel(
                    customerDataServiceFeignClient.inquiryHistoricalCases(httpHeaders, request)
            );
        } catch (FeignException e) {
            log.error("Error occur when Calling POST /v1/customer-data-service/common-lov/historical-cases/inquiry");
            if (ExceptionUtil.isTimeoutException(e)) {
                throw new GenericException(ResponseCodeEnum.INTERNAL_SERVER_TIMEOUT_ERROR_CODE);
            }
            throw e;
        }
    }
}
