package com.ttb.top.web.customer360service.controller.v1.implement;


import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.web.customer360service.controller.v1.CustomerPhoneNumbersController;
import com.ttb.top.web.customer360service.model.customerphonenumbers.request.CustomerPhoneNumbersRequest;
import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse;
import com.ttb.top.web.customer360service.service.customerphonenumbers.CustomerPhoneNumbersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class CustomerPhoneNumbersControllerImpl implements CustomerPhoneNumbersController {

    private final CustomerPhoneNumbersService customerPhoneNumbersService;

    @Override
    public ResponseModel<CustomerPhoneNumbersResponse> getCustomerPhoneNumbers(CustomerPhoneNumbersRequest request) {
        return ResponseModel.success(customerPhoneNumbersService.getCustomerPhoneNumbers((request)));
    }
}
