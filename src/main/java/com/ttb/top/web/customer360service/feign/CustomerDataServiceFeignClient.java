package com.ttb.top.web.customer360service.feign;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.web.customer360service.configuration.Customer360CustomFeignConfig;
import com.ttb.top.web.customer360service.model.feign.customer360.request.FeignCaseComplainInquiryRequest;
import com.ttb.top.web.customer360service.model.feign.customer360.response.FeignCaseComplainInquiryResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomerConsentPdpaRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomersDNCsInquiryRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignGetDipchipImageRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignHistoricalCaseNtbRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignInquiryHistoricalCasesRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignPromptpayCitizenRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignPromptpayMobileRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignTTBTouchCustomerStatusRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerConsentPdpaResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomersDNCsInquiryResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignGetDipchipImageResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignHistoricalCaseNtbResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignInquiryHistoricalCasesResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignPromptpayCitizenResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignPromptpayMobileResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignTTBTouchCustomerStatusResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerConsentsTaxResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomerConsentsTaxRequest;
import com.ttb.top.web.customer360service.model.feign.customerdata.response.FeignCustomerProfileCustomerResponse;
import com.ttb.top.web.customer360service.model.feign.customerdata.request.FeignCustomerProfileCustomerRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(
        name = "${feign.customer-data-service.name}",
        url = "${feign.customer-data-service.url}",
        configuration = Customer360CustomFeignConfig.class
)
public interface CustomerDataServiceFeignClient {
    @PostMapping("${feign.customer-data-service.customer-profile-dipchip-image.endpoint}")
    ResponseModel<FeignGetDipchipImageResponse> getDipchipImage(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignGetDipchipImageRequest request
    );

    @PostMapping("${feign.customer-data-service.ttbtouch-customer-status.endpoint}")
    ResponseModel<FeignTTBTouchCustomerStatusResponse> getTTBTouchCustomerStatus(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignTTBTouchCustomerStatusRequest request
    );

    @PostMapping("${feign.customer-data-service.historical-case.advance-search-lov-inquiry.endpoint}")
    ResponseModel<FeignInquiryHistoricalCasesResponse> inquiryHistoricalCases(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignInquiryHistoricalCasesRequest request
    );

    @PostMapping("${feign.customer-data-service.case-complain.inquiry.endpoint}")
    ResponseModel<List<FeignCaseComplainInquiryResponse>> getCaseComplainInquiry(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignCaseComplainInquiryRequest request
    );

    @PostMapping("${feign.customer-data-service.promptpay-mobile.endpoint}")
    ResponseModel<FeignPromptpayMobileResponse> getPromptpayMobile(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignPromptpayMobileRequest request
    );

    @PostMapping("${feign.customer-data-service.promptpay-citizen.endpoint}")
    ResponseModel<FeignPromptpayCitizenResponse> getPromptpayCitizen(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignPromptpayCitizenRequest request
    );

    @PostMapping("${feign.customer-data-service.dncs.endpoint}")
    ResponseModel<FeignCustomersDNCsInquiryResponse> inquiryDNCsCustomer(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignCustomersDNCsInquiryRequest request
    );

    @PostMapping("${feign.customer-data-service.consents-consents.pdpa.inquiry.endpoint}")
    ResponseModel<FeignCustomerConsentPdpaResponse> getPdpaConsents(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignCustomerConsentPdpaRequest request
    );

    @PostMapping("${feign.customer-data-service.customer-consents.tax.inquiry.endpoint}")
    ResponseModel<FeignCustomerConsentsTaxResponse> getTaxConsents(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignCustomerConsentsTaxRequest request
    );

    @PostMapping("${feign.customer-data-service.crm.historical-case-ntb.endpoint}")
    ResponseModel<List<FeignHistoricalCaseNtbResponse>> getHistoricalCaseNtb(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignHistoricalCaseNtbRequest request
    );


    @PostMapping("${feign.customer-data-service.phone-numbers.endpoint}")
    ResponseModel<FeignCustomerProfileCustomerResponse> getPhoneNumbers(
            @RequestHeader HttpHeaders header,
            @RequestBody FeignCustomerProfileCustomerRequest request
    );
}