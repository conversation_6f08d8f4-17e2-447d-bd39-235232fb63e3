package com.ttb.top.web.customer360service.controller.v1;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.decryption.annotation.Decrypt;
import com.ttb.top.library.decryption.annotation.EncryptResponseBody;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseLandingRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.HistoricalCaseTouchPointHistoryRequest;
import com.ttb.top.web.customer360service.model.historicalcase.request.Landing360NtbRequest;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLandingResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseTouchPointHistoryResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.HistoricalCaseLanding360NtbResponse;
import com.ttb.top.web.customer360service.model.historicalcase.response.Type;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

import static com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant.STAFF_ID;

@Validated
@EnableLookup
@RequestMapping("/historical-case")
public interface HistoricalCaseController {

    @PostMapping("/landing/inquiry")
    ResponseModel<HistoricalCaseLandingResponse> getHistoricalCaseLanding(
            @Valid @Decrypt @RequestBody HistoricalCaseLandingRequest request
    );

    @PostMapping("/advance-search-lov/inquiry")
    ResponseModel<Map<String, List<Type>>> inquiryAdvanceSearchLov(
            @RequestHeader(value = STAFF_ID) String staffId
    );

    @PostMapping("/touchpoint-history/inquiry")
    @EncryptResponseBody
    ResponseModel<HistoricalCaseTouchPointHistoryResponse> getHistoricalCaseTouchPointHistory(
            @Valid @Decrypt @RequestBody HistoricalCaseTouchPointHistoryRequest request
    );

    @PostMapping("/landing360-ntb/inquiry")
    ResponseModel<List<HistoricalCaseLanding360NtbResponse>> getLanding360NTB(
            @Valid @Decrypt @RequestBody Landing360NtbRequest request
    );
}
