package com.ttb.top.web.customer360service.model.customerphonenumbers.response;

import com.ttb.top.library.decryption.annotation.Encrypted;
import com.ttb.top.library.decryption.model.CryptSupported;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerPhoneNumbersResponse implements CryptSupported {
    @Encrypted
    private Phone phonesM;
    @Encrypted
    private Phone phonesB;
    @Encrypted
    private Phone phonesR;

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Phone implements CryptSupported {
        private String identPhone;
        private String phoneSeq;
        private String phoneType;
        private String phonePrefix;
        @Encrypted
        private String phoneNo;
        @Encrypted
        private String phoneNoFull;
        private String phoneNoTo;
        private String phoneNoExt;
        private String createDate;
        private String createBy;
        private String updateDate;
        private String updateBy;
    }
}
