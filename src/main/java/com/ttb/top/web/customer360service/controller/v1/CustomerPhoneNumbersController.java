package com.ttb.top.web.customer360service.controller.v1;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.decryption.annotation.Decrypt;
import com.ttb.top.library.decryption.annotation.EncryptResponseBody;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.web.customer360service.model.customerphonenumbers.request.CustomerPhoneNumbersRequest;
import com.ttb.top.web.customer360service.model.customerphonenumbers.response.CustomerPhoneNumbersResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


@Validated
@EnableLookup
@RequestMapping
public interface CustomerPhoneNumbersController {
    @PostMapping("/customer-phone-numbers")
    @EncryptResponseBody
    ResponseModel<CustomerPhoneNumbersResponse> getCustomerPhoneNumbers(
            @Valid @Decrypt @RequestBody CustomerPhoneNumbersRequest request
    );
}
